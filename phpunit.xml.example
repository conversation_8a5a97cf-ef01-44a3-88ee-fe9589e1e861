<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
>
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
    </testsuites>
    <source>
        <include>
            <directory>app</directory>
        </include>
    </source>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="APP_MAINTENANCE_DRIVER" value="file"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_STORE" value="array"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="PULSE_ENABLED" value="false"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
        <env name="REDIS_CLIENT" value="predis"/>
        <env name="NIGHTWATCH_ENABLED" value="false"/>
        <env name="DB_CONNECTION" value="mysql"/>
        <env name="DB_DATABASE" value="tornado-testing"/>
        <env name="DB_HOST" value="127.0.0.1"/>
        <env name="DB_PORT" value="3307"/>
        <env name="DB_USERNAME" value="root"/>
        <env name="DB_PASSWORD" value="root"/>
        <env name="LOG_CHANNEL" value="stack"/>
        <env name="LOG_LEVEL" value="debug"/>
        <env name="CAPTCHA_ENABLED" value="false"/>
        <env name="SENHUO_DEVELOPMENT" value="true"/>
        <env name="NEW_SAUDI_COMMISSION_ENABLED" value="false"/>
    </php>
</phpunit>
