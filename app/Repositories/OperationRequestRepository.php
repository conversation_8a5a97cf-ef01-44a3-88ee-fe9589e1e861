<?php

namespace App\Repositories;

use App\Constants\OperationRequestStatus;
use App\Constants\TransactionType;
use App\Models\OperationRequest;
use App\Models\Store;
use App\Models\TransactionLog;
use App\Utils\Gmt8;
use App\Utils\Math;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class OperationRequestRepository
{
    public function createOperationRequest(
        int $user_id,
        int $store_id,
        int $order_quantity,
        float $order_profit,
        float $profit_percentage,
        float $profit,
        float $cost,
        int $type,
    ) {
        return OperationRequest::create([
            'user_id' => $user_id,
            'store_id' => $store_id,
            'order_quantity' => $order_quantity,
            'order_profit' => $order_profit,
            'profit_percentage' => $profit_percentage,
            'type' => $type,
            'profit' => $profit,
            'cost' => $cost,
            'status' => OperationRequestStatus::Pending,
        ]);
    }

    public function getOperationStatisticByMonth(int $userId, string $date, $type = null)
    {
        if ($type == null) {
            $type = [
                TransactionType::OperationServiceProfit->value,
                TransactionType::StoreDailyProfit->value,
            ];
        }
        $dateRange = $this->convertDate2DateMonthRange($date);
        $startDate = Gmt8::toUtc($dateRange['startDate']);
        $endDate = Gmt8::toUtc($dateRange['endDate']);

        // Apply type filter dynamically
        $types = $type
            ? (array) $type
            : [
                TransactionType::OperationServiceProfit->value,
                TransactionType::StoreDailyProfit->value,
            ];

        // Convert types array into a string for raw SQL query
        $typeString = '"' . implode('","', $types) . '"';

        return TransactionLog::select([
            DB::raw('COUNT(id) as total_operation_times'),
            DB::raw('SUM(amount) as month_profit'),
            DB::raw('(
            SELECT SUM(amount)
            FROM transaction_logs
            WHERE user_id = ' . $userId . '
            AND type IN (' . $typeString . ')
        ) as total_profit'),
        ])
            ->where('user_id', $userId)
            ->whereIn('type', $types)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->first();
    }

    public function getOperationStatisticToday(int $userId, $type = null): array
    {
        $dateRange = Gmt8::toUtcTodayRange(Gmt8::today());
        [$startDate, $endDate] = $dateRange;

        // Operation Requests (Free)
        $freeData = OperationRequest::where('user_id', $userId)
            ->where('status', OperationRequestStatus::Success->value)
            ->select([
                DB::raw('SUM(order_profit) as total_gross_profit'),
                DB::raw('SUM(cost) as total_paid_cost'),
                DB::raw('SUM(profit) as total_net_profit'),
            ])
            ->first();
        $todayFreeGrossProfit = OperationRequest::where('user_id', $userId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('order_profit');

        // Stores (Paid)
        $storeData = Store::where('user_id', $userId)
            ->where('type', 2)
            ->selectRaw('
            SUM(daily_profit * release_times) * 0.9 as total_paid_cost,
            SUM(daily_profit * release_times) * 0.1 as total_net_profit,
            SUM(daily_profit ) as today_gross_profit
        ')
            ->first();

        // Aggregate results
        $todayTotalGrossProfit = Math::formatNumber($todayFreeGrossProfit + $storeData->today_gross_profit, true);
        $totalPaidCost = Math::formatNumber($freeData->total_paid_cost + $storeData->total_paid_cost, true);
        $totalNetProfit = Math::formatNumber($freeData->total_net_profit + $storeData->total_net_profit, true);

        // Filter based on type
        return match ($type) {
            TransactionType::OperationServiceProfit->value => [
                'today_total_gross_profit' => Math::formatNumber($todayFreeGrossProfit, true),
                'total_paid_cost' => Math::formatNumber($freeData->total_paid_cost, true),
                'total_net_profit' => Math::formatNumber($freeData->total_net_profit, true),
            ],
            TransactionType::StoreDailyProfit->value => [
                'today_total_gross_profit' => Math::formatNumber($storeData->today_gross_profit, true),
                'total_paid_cost' => Math::formatNumber($storeData->total_paid_cost, true),
                'total_net_profit' => Math::formatNumber($storeData->total_net_profit, true),
            ],
            default => [
                'today_total_gross_profit' => $todayTotalGrossProfit,
                'total_paid_cost' => $totalPaidCost,
                'total_net_profit' => $totalNetProfit,
            ],
        };
    }

    public function getOperationRequestList(int $userId, array $validated): LengthAwarePaginator
    {
        $query = TransactionLog::where('user_id', $userId)
            ->when(Arr::get($validated, 'type'), function ($query, $type) {
                return $query->where('type', $type);
            })
            ->when(Arr::get($validated, 'date'), function ($query, $date) {
                $dateRange = $this->convertDate2DateMonthRange($date);
                $startDate = Gmt8::toUtc($dateRange['startDate']);
                $endDate = Gmt8::toUtc($dateRange['endDate']);

                return $query->whereBetween('created_at', [$startDate, $endDate]);
            })
            ->orderByDesc('created_at');
        if (!in_array(Arr::get($validated, 'type'), [TransactionType::StoreDailyProfit->value, TransactionType::OperationServiceProfit->value])) {
            $query->whereIn('type', [TransactionType::StoreDailyProfit->value, TransactionType::OperationServiceProfit->value]);
        }
        $query->groupBy([DB::raw('date(created_at)'), 'reference']);

        $query->select([
            DB::raw('SUM(amount) as amount'),
            'transaction_logs.reference',
            DB::raw('min(transaction_logs.created_at) as created_at'),
        ]);
        $query->with(['store', 'operationRequest.store']);

        return $query->paginate(Arr::get($validated, 'per_page', 20));
    }

    private function convertDate2DateMonthRange(string $date): array
    {
        [$year, $month] = explode('-', $date);
        $startDate = Carbon::create($year, $month)->format('Y-m-d H:i:s');
        $endDate = Carbon::create($year, $month)->endOfMonth()->format('Y-m-d H:i:s');

        return [
            'startDate' => $startDate,
            'endDate' => $endDate,
        ];
    }

    private function convertDate2DateYearRange(string $year): array
    {
        $startDate = Carbon::create($year)->startOfYear()->format('Y-m-d H:i:s');
        $endDate = Carbon::create($year)->endOfYear()->format('Y-m-d H:i:s');

        return [
            'startDate' => $startDate,
            'endDate' => $endDate,
        ];
    }

    public function getOperationStatisticByYear(int $userId, string $year, $type = null)
    {
        if ($type == null) {
            $type = [
                TransactionType::OperationServiceProfit->value,
                TransactionType::StoreDailyProfit->value,
            ];
        }
        $dateRange = $this->convertDate2DateYearRange($year);
        $startDate = Gmt8::toUtc($dateRange['startDate']);
        $endDate = Gmt8::toUtc($dateRange['endDate']);

        // Apply type filter dynamically
        $types = $type
            ? (array) $type
            : [
                TransactionType::OperationServiceProfit->value,
                TransactionType::StoreDailyProfit->value,
            ];

        // Convert types array into a string for raw SQL query
        $typeString = '"' . implode('","', $types) . '"';

        return TransactionLog::select([
            DB::raw('(select count(days) from (select date(created_at) as days,count(id)
                    from transaction_logs
            WHERE user_id = ' . $userId . '
            AND type IN (' . $typeString . ')' . '
            and created_at >= "' . $startDate . '" and created_at <= "' . $endDate . '"
            group by date(created_at)
            )as request_days) as total_operation_times'),
            DB::raw('SUM(amount) as year_profit'),
            DB::raw('(
            SELECT SUM(amount)
            FROM transaction_logs
            WHERE user_id = ' . $userId . '
            AND type IN (' . $typeString . ')
        ) as total_profit'),
        ])
            ->where('user_id', $userId)
            ->whereIn('type', $types)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->first();
    }
}
