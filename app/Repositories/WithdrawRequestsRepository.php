<?php

namespace App\Repositories;

use App\Models\WithdrawRequest;

class WithdrawRequestsRepository
{
    public static function getWithdrawDetails(string $orderExternalId): ?WithdrawRequest
    {
        return WithdrawRequest::whereOrderExternalId($orderExternalId)->first();
    }

    public static function updateConversionData(WithdrawRequest $withdrawRequest, array $data): WithdrawRequest
    {
        // Update conversion data if available
        if (isset($data['converted_amount'])) {
            $withdrawRequest->converted_amount = $data['converted_amount'];
        }
        if (isset($data['converted_currency'])) {
            $withdrawRequest->converted_currency = $data['converted_currency'];
        }
        if (isset($data['exchange_rate'])) {
            $withdrawRequest->exchange_rate = $data['exchange_rate'];
        }
        if (isset($data['exchange_rate_time'])) {
            $withdrawRequest->exchange_rate_time = now()->parse($data['exchange_rate_time']);
        }

        $withdrawRequest->save();

        return $withdrawRequest;
    }
}
